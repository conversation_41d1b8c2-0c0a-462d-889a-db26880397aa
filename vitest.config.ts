import { defineConfig } from 'vitest/config'
import { resolve } from 'path'

export default defineConfig({
  test: {
    // 测试环境配置
    environment: 'jsdom', // 使用 jsdom 环境来模拟浏览器环境

    // 全局设置
    globals: true, // 启用全局 API (describe, it, expect 等)

    // 测试设置文件
    setupFiles: ['./tests/setup.ts'],

    // 包含的测试文件模式
    include: [
      'src/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}',
      'tests/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'
    ],

    // 排除的文件
    exclude: [
      'node_modules',
      'out',
      'dist',
      '.electron-vite'
    ],

    // 覆盖率配置
    coverage: {
      provider: 'c8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'out/',
        'dist/',
        'tests/',
        '**/*.d.ts',
        '**/*.config.*',
        '**/coverage/**'
      ]
    },

    // 测试超时设置
    testTimeout: 10000,
    hookTimeout: 10000,

    // 设置测试文件的运行模式
    pool: 'threads',
    poolOptions: {
      threads: {
        singleThread: true // 对于 Electron 测试，使用单线程可能更稳定
      }
    }
  },

  // 路径解析配置
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@main': resolve(__dirname, 'src/main'),
      '@preload': resolve(__dirname, 'src/preload'),
      '@renderer': resolve(__dirname, 'src/renderer'),
      '@core': resolve(__dirname, 'src/core')
    }
  },

  // 定义全局变量
  define: {
    __DEV__: true,
    __TEST__: true
  }
})
