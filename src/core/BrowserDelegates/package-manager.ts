import path from "path"
import fs from "fs-extra"
import logger from 'electron-log'
import bridge from '../MessageBridge'
import decompress from 'decompress'
import semver from 'semver'
import { deleteAsync } from 'del'

// const md5file = import('md5-file') // Using require to avoid TypeScript issues with md5-file
// import md5file from 'md5-file' // Use promises version for better async handling
const md5file = require('md5-file') // Use promises version for better async handling

import { PACKAGE_PATH, ASSETS_PATH, INSTALLED_META_FILE, PACKAGE_META_FILE } from '../Configure'
let tasks_list: { [identity: string]: any } = {}

function getLocalPackageMetaFilePath(pack: string) {
	return path.join(PACKAGE_PATH, pack, PACKAGE_META_FILE)
}

function getLocalInstalledMetaFilePath(pack: string) {
	return path.join(ASSETS_PATH, pack, INSTALLED_META_FILE)
}

export async function getLocalPackageVersion({ pack }: { pack: string }) {
	let meta = getLocalPackageMetaFilePath(pack)
	try {
		await fs.ensureFile(meta)
		let content = await fs.readFile(meta, "utf8")
		if (content) {
			try {
				content = JSON.parse(content)
				return content
			} catch (e) {
				return
			}
		}
	} catch (e) {
		return
	}
}

export async function getDomains({ url }: { url: string }) {
	url = `${url}?t=${new Date().getTime()}`
	const { got } = await import('got')
	let response = await got.get(url, {
		responseType: 'json',
		https: { rejectUnauthorized: false }
	})
	return response.body
}

export async function getServerPackageVersion({ url }: { url: string }): Promise<any> {
	url = `${url}?t=${new Date().getTime()}`
	const { got } = await import('got')
	let response = await got.get(url, {
		responseType: 'json',
		https: { rejectUnauthorized: false }
	})
	return response.body
}

export async function isUpdateAvailable({ url, pack, checkVersionOnly }: { url: string, pack: string, checkVersionOnly?: boolean }) {
	let serverInfo = await getServerPackageVersion({ url }),
		localInfo = await getLocalInstalledVersion({ pack })
	if (!serverInfo || !semver.valid(serverInfo.version)) {
		if (localInfo) {
			return { available: false, local: localInfo }
		} else {
			throw new Error("获取远程更新出错，且本地未安装。")
		}
	}
	if (!localInfo || !semver.valid(localInfo.version)) {
		return { available: true, local: null, server: serverInfo }
	}
	let result = {
		local: localInfo,
		server: serverInfo,
		available: false
	}
	if (checkVersionOnly) {
		result.available = semver.gt(serverInfo.version, localInfo.version)
		return result
	} else {
		result.available = semver.gt(serverInfo.version, localInfo.version) ||
			(semver.eq(serverInfo.version, localInfo.version) && serverInfo.md5 != localInfo.md5)
		return result
	}
}

export async function getLocalInstalledVersion({ pack }: { pack: string }): Promise<any> {
	let meta = getLocalInstalledMetaFilePath(pack)
	try {
		await fs.ensureFile(meta)
		let content = await fs.readFile(meta, "utf8")
		if (content) {
			try {
				content = JSON.parse(content)
				return content
			} catch (e) {
				return
			}
		}
	} catch (err) {
		return
	}
}

export async function clearCachedData({ packs = [] }: { packs: any[] }) {
	let dirsToDel: string[] = []
	packs.forEach(pack => {
		dirsToDel.push(path.join(PACKAGE_PATH, pack.name))
		if (!pack.packageOnly) {
			dirsToDel.push(path.join(ASSETS_PATH, pack.name))
		} else if (pack.clearAssetsLater) {
			dirsToDel.push(path.join(ASSETS_PATH, pack.name, INSTALLED_META_FILE))
		}
	})
	if (dirsToDel.length > 0) {
		await deleteAsync(dirsToDel, { force: true })
	}
}

export async function getDownloadTask({ identity }: { identity: string }) {
	return tasks_list[identity] || null
}

export async function startDownloadTask({ pack, url, md5, version, autoUnzip, checksum }: any, sender: any) {
	let filename = `v${version}${md5 ? ("-build." + md5) : ""}.zip`
	let identity = `${pack}/${filename}`,
		destpath = `${PACKAGE_PATH}/${identity}`
	if (fs.existsSync(destpath)) {
		await deleteAsync(destpath, { force: true })
		logger.info(`delete local file ${destpath}`)
	}
	if (tasks_list[identity]) {
		tasks_list[identity].abort()
		delete tasks_list[identity]
	}
	let task: any, error: any, lastPercent = 0
	try {
		const { got } = await import('got')
		task = got(url, {
			timeout: { socket: 60000 },
			https: { rejectUnauthorized: false },
			responseType: 'buffer'
		})
	} catch (e) {
		error = e
	}
	if (
		!task ||
		typeof task.on !== 'function' ||
		typeof error !== 'undefined'
	) {
		if (!error) {
			error = new Error('unknown error');
		}
		if (sender) {
			if (bridge && typeof bridge.call === 'function') {
				bridge?.call({ method: `${identity}/error`, args: error, sender }).catch(err => {
					console.log("error", err)
				})
			}
		}
		logger.error('[Download Error]', error.message);
		logger.error(error.stack);
		return;
	}
	task.on('downloadProgress', (progress: any) => {
		if (task.isCanceled) return
		let { total, transferred, percent } = progress;
		percent = parseFloat(percent.toFixed(2));
		if (percent > lastPercent) {
			logger.info(`download ${identity} [${transferred}/${total}] - ${percent * 100}%`);
			lastPercent = percent
			bridge.call({
				method: `${identity}/progress`,
				args: { total, transferred, percent },
				sender
			}).catch(err => {
				console.log("error", err)
			})
		}
	})
	tasks_list[identity] = {
		identity,
		abort: () => {
			if (task && !task.isCanceled) {
				task.cancel()
			}
		},
		task: task.then((response: any) => {
			return fs.outputFile(destpath, response.body);
		})
			.then(() => {
				return new Promise((resolve, reject) => {
					if (checksum && md5) {
						(async () => {
							try {
								md5file(destpath, async (err: any, hash: string) => {
									if (err) {
										reject(new Error("计算文件md5出错"))
										return
									}
									if (hash == md5) {
										logger.info(`check md5 sucess ${identity} ${md5}`)
										resolve(true)
									} else {
										deleteAsync(destpath, { force: true }).then(() => {
											reject(new Error("下载文件完整性校验失败"))
										});
									}
								})
							} catch (err) {
								reject(new Error("计算文件md5出错"))
							}
						})()
					} else {
						logger.info(`no need to check md5 ${identity}`)
						resolve(true)
					}
				})
			})
			.then(() => {
				// write downloaded version to local
				let metaContent = JSON.stringify({ version, md5, file: filename })
				logger.info(`write to local package for version ${metaContent}`)
				let metaFilePath = getLocalPackageMetaFilePath(pack)
				try {
					fs.writeFileSync(metaFilePath, metaContent, 'utf8')
				} catch (err) {
					throw new Error("写入本地文件包版本号出错")
				}
				if (autoUnzip) {
					return decompressZip({ pack, file: destpath }).then(() => {
						logger.info(`write to local installed package for version ${metaContent}`)
						metaFilePath = getLocalInstalledMetaFilePath(pack)
						try {
							fs.writeFileSync(metaFilePath, metaContent, 'utf8')
						} catch (err) {
							throw new Error("写入本地安装版本号出错")
						}
					})
				}
			})
			.then(() => {
				logger.info(`downloaded ${identity}, ${url}, ${destpath}`)
				bridge.call({
					method: `${identity}/success`, args: { identity, destpath }, sender
				}).catch(err => {
					console.log("error", err)
				})
			})
	};
	tasks_list[identity].task.catch((err: any) => {
		err = err || new Error('unknown error')
		bridge.call({
			method: `${identity}/error`,
			args: err.message, sender
		}).catch(err => {
			console.log("error", err)
		})
		logger.error('[Download Error]', err.message)
		logger.error(err.stack)
	})
		.then(() => {
			delete tasks_list[identity]
		});
	return tasks_list[identity]
}

export async function abortDownloadTask({ identity }: { identity: string }) {
	if (identity in tasks_list) {
		tasks_list[identity].abort()
		delete tasks_list[identity]
	}
}

export async function abortAllDownloadTask() {
	for (const identity in tasks_list) {
		tasks_list[identity].abort()
		delete tasks_list[identity]
	}
}

export async function decompressZip({ pack, file }: { pack: string, file: string }) {
	try {
		await fs.ensureFile(file)
		const unpackDir = `${ASSETS_PATH}/${pack}_${new Date().getTime()}`,
			destDir = `${ASSETS_PATH}/${pack}`
		await decompress(file, unpackDir)
		await deleteAsync(destDir, { force: true })
		await fs.move(unpackDir, destDir)
		return true
	} catch (err) {
		console.error(err)
		return
	}
}
