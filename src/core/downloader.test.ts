import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { Downloader, DownloadOptions } from './downloader'
import path from 'path'

// Mock got
const mockGot = vi.fn()
vi.mock('got', () => ({
	default: mockGot,
	got: mockGot
}))

// Mock fs-extra
vi.mock('fs-extra', () => ({
	default: {
		ensureDir: vi.fn(),
		outputFile: vi.fn(),
		readFileSync: vi.fn()
	},
	ensureDir: vi.fn(),
	outputFile: vi.fn(),
	readFileSync: vi.fn()
}))

// Mock ts-md5
const mockHasher = {
	hash: vi.fn()
}
vi.mock('ts-md5', () => ({
	ParallelHasher: vi.fn(() => mockHasher)
}))

describe('Downloader', () => {
	let downloader: Downloader
	let testOptions: DownloadOptions
	let mockFs: any

	beforeEach(async () => {
		vi.clearAllMocks()

		// Get mocked fs-extra
		const fsModule = await import('fs-extra')
		mockFs = fsModule.default

		testOptions = {
			cache_path: path.join(__dirname, '..', '..', 'test-downloads'),
			package_name: 'test-file.zip',
			url: 'https://httpbin.org/bytes/1024'
		}

		downloader = new Downloader(testOptions)
	})

	afterEach(() => {
		if (downloader) {
			downloader.cancel()
		}
	})

	describe('Constructor', () => {
		it('should create a downloader instance', () => {
			expect(downloader).toBeInstanceOf(Downloader)
		})

		it('should set properties correctly', () => {
			const opts: DownloadOptions = {
				cache_path: '/test/path',
				package_name: 'test.zip',
				url: 'https://example.com/file.zip',
				md5: 'abc123'
			}
			const dl = new Downloader(opts)
			expect(dl).toBeInstanceOf(Downloader)
		})
	})

	describe('Basic Functionality', () => {
		it('should handle successful download', async () => {
			const mockResponse = {
				statusCode: 200,
				body: Buffer.from('test content')
			}

			const mockTask = Object.assign(Promise.resolve(mockResponse), {
				on: vi.fn(),
				isCanceled: false,
				cancel: vi.fn()
			})

			mockGot.mockReturnValue(mockTask)
			mockFs.ensureDir.mockResolvedValue(undefined)
			mockFs.outputFile.mockResolvedValue(undefined)

			const completeSpy = vi.fn()
			downloader.on('complete', completeSpy)

			await downloader.start()

			expect(completeSpy).toHaveBeenCalled()
		})

		it('should emit progress events', async () => {
			const mockResponse = {
				statusCode: 200,
				body: Buffer.from('test')
			}

			const mockTask = Object.assign(Promise.resolve(mockResponse), {
				on: vi.fn(),
				isCanceled: false,
				cancel: vi.fn()
			})

			mockGot.mockReturnValue(mockTask)
			mockFs.ensureDir.mockResolvedValue(undefined)
			mockFs.outputFile.mockResolvedValue(undefined)

			const progressSpy = vi.fn()
			downloader.on('progress', progressSpy)

			// Simulate progress event
			mockTask.on.mockImplementation((event: string, callback: (progress: any) => void) => {
				if (event === 'downloadProgress') {
					callback({ total: 1000, transferred: 500, percent: 0.5 })
				}
			})

			await downloader.start()

			expect(progressSpy).toHaveBeenCalledWith({
				total: 1000,
				transferred: 500,
				percent: 0.5
			})
		})

		it('should handle MD5 verification', async () => {
			const testMd5 = 'abc123'
			const downloaderWithMd5 = new Downloader({
				...testOptions,
				md5: testMd5
			})

			const mockResponse = {
				statusCode: 200,
				body: Buffer.from('test content')
			}

			const mockTask = Object.assign(Promise.resolve(mockResponse), {
				on: vi.fn(),
				isCanceled: false,
				cancel: vi.fn()
			})
			mockGot.mockReturnValue(mockTask)
			mockFs.ensureDir.mockResolvedValue(undefined)
			mockFs.outputFile.mockResolvedValue(undefined)
			mockFs.readFileSync.mockReturnValue('test content')
			mockHasher.hash.mockResolvedValue(testMd5)

			const completeSpy = vi.fn()
			downloaderWithMd5.on('complete', completeSpy)

			await downloaderWithMd5.start()

			expect(completeSpy).toHaveBeenCalled()
		})

		it('should handle HTTP errors', async () => {
			const mockResponse = {
				statusCode: 404,
				statusMessage: 'Not Found',
				body: Buffer.from('Not found')
			}

			const mockTask = Object.assign(Promise.resolve(mockResponse), {
				on: vi.fn(),
				isCanceled: false,
				cancel: vi.fn()
			})
			mockGot.mockReturnValue(mockTask)
			mockFs.ensureDir.mockResolvedValue(undefined)
			mockFs.outputFile.mockResolvedValue(undefined)

			const errorSpy = vi.fn()
			downloader.on('error', errorSpy)

			await downloader.start()

			// The current implementation emits HTTP errors and stops processing
			expect(errorSpy).toHaveBeenCalledWith(
				expect.objectContaining({
					message: 'HTTP 404: Not Found'
				})
			)
			// Should not save file when HTTP error occurs
			expect(mockFs.outputFile).not.toHaveBeenCalled()
		})

		it('should handle file write errors', async () => {
			const mockResponse = {
				statusCode: 200,
				body: Buffer.from('test content')
			}

			const mockTask = Object.assign(Promise.resolve(mockResponse), {
				on: vi.fn(),
				isCanceled: false,
				cancel: vi.fn()
			})
			mockGot.mockReturnValue(mockTask)
			mockFs.ensureDir.mockResolvedValue(undefined)
			mockFs.outputFile.mockRejectedValue(new Error('Permission denied'))

			const errorSpy = vi.fn()
			downloader.on('error', errorSpy)

			// The downloader catches errors and emits them instead of re-throwing
			await downloader.start()

			expect(errorSpy).toHaveBeenCalledWith(
				expect.objectContaining({
					message: 'Permission denied'
				})
			)
		})

		it('should handle download cancellation', () => {
			const mockTask = {
				on: vi.fn(),
				isCanceled: false,
				cancel: vi.fn(() => {
					mockTask.isCanceled = true
				})
			}

			// Simulate having a download task
			;(downloader as any).downloadTask = mockTask

			const canceled = downloader.cancel()

			expect(canceled).toBe(true) // canceled should be true
			expect(mockTask.cancel).toHaveBeenCalled()
		})

		it('should handle cancel when no download task exists', () => {
			// Should not throw error when no download task exists
			expect(() => downloader.cancel()).not.toThrow()
		})
	})
})
